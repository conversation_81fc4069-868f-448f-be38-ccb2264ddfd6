namespace Dolo.Bot.Shop.Hub.Ticket;

[Command("ticket")]
[Description("ticket commands")]
public partial class Ticket 
{
    public static async Task<DiscordChannel> GetArchiveCategoryAsync()
    {
        // we are going to automatically find and create the archive channels
        // before it was hardcoded but now we are going to make it dynamic

        var channels = await Hub.Guild!.GetChannelsAsync();
        var lastArchiveChannel = channels.LastOrDefault(a => a.IsCategory && a.Name.Contains("Ticket Archive") && a.Children.Count < 50);
        var previousArchive = channels.Last(a => a.IsCategory             && a.Name.Contains("Ticket Archive"));

        // if we have a archive channel we are going to return it
        if (lastArchiveChannel is {})
            return lastArchiveChannel;

        // when we have no archive channels we are going to create them
        // based on the previous archive channel number we are naming the channel
        // to the next number
        var previousNumber = int.TryParse(previousArchive.Name.Split(" ").Last(), out var number) ? number : 0;
        var archive = await Hub.Guild.CreateChannelCategoryAsync($"Ticket Archive {previousNumber + 1}");
        return archive;
    }
}