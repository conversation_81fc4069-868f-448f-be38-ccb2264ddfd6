using Dolo.Core.AMF3;
using Dolo.Core.Consola;
using Dolo.Core.Http;
using Dolo.Planet.Entities;
using Dolo.Planet.Enums;
using Dolo.Planet.NET.Entities;
using Dolo.Planet.Utils;
using MethodTimer;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
namespace Dolo.Planet.NET;

internal partial class MspApi
{

    /// <summary>
    ///     Sends a request asynchronously using the provided RestConfig.
    /// </summary>
    /// <typeparam name="T">The type of the result expected from the request.</typeparam>
    /// <param name="config">The RestConfig instance to use for sending the request.</param>
    /// <returns>A Task that represents the asynchronous operation. The task result contains a MspResult of type T.</returns>
    public async Task<MspResult<T>> SendAsync<T>(RestConfig config)
    {
        // Send the request asynchronously and await the response.
        var req = await SendCustomAsync<T>(config);

        // Return a new MspResult with the response data.
        return new()
        {
            HttpRequest = req?.Request,
            HttpException = req?.Exception,
            HttpResponse = req?.Response,
            MovieStarPlanet = _client,
            Success = req is { Success: true },
            Value = req is { } ? req.Result : default
        };
    }

    /// <summary>
    ///     Sends a request asynchronously using the provided RestConfig.
    ///     This is a convenience method that calls SendAsync<T> with T set to object.
    /// </summary>
    /// <param name="config">The RestConfig instance to use for sending the request.</param>
    /// <returns>A Task that represents the asynchronous operation. The task result contains a MspResult of type object.</returns>
    public async Task<MspResult<object>> SendAsync(RestConfig config) => await SendAsync<object>(config);


    /// <summary>
    ///     public method to execute a request using a retry system.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="method"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    internal async Task<RestResponse<T>> SendAsync<T>([CallerMemberName] string method = "", params object?[]? data)
    {
        var request = await InternalSendAsync<T>(new()
        {
            Method = method,
            Data = data
        });
        return request;
    }

    /// <summary>
    ///     public method to execute a request using a retry system.
    ///     and a cacnelation token.
    /// </summary>
    /// <returns></returns>
    internal async Task<RestResponse<T>> SendAsync<T>([CallerMemberName] string method = "", RestConfig? config = null)
    {
        config ??= new();
        var request = await InternalSendAsync<T>(config.SetMethod(method));
        return request;
    }


    /// <summary>
    ///     public method to execute a request using a retry system.
    /// </summary>
    /// <returns></returns>
    private async Task<RestResponse<T>?> SendCustomAsync<T>(RestConfig config)
    {
        var request = await InternalSendAsync<T>(new(config.SetCustom()));
        return request;
    }


    /// <summary>
    ///     private method to execute a request.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="method"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    [Time]
    [SuppressMessage("ReSharper", "InvalidXmlDocComment")]
    [Obfuscation(Feature = "internalization", Exclude = true)]
    [Obfuscation(Feature = "renaming", Exclude = true)]
    private async Task<RestResponse<T>> InternalSendAsync<T>(RestConfig config)
    {
        var requestId = Guid.NewGuid().ToString("N")[..8];
        _logger.LogInformation("🚀 [REQ-{RequestId}] Starting request for method '{Method}'", requestId, config.Method);
        
        try
        {
            _endpoint ??= await MspClientUtil.GetServiceEndpointAsync(_client.Config.Server.GetValueOrDefault());
            _logger.LogDebug("📡 [REQ-{RequestId}] Service endpoint resolved: {EndpointCount} services available", requestId, _endpoint?.Services?.Count() ?? 0);

            if (!_endpoint.Services.Any())
            {
                _logger.LogError("❌ [REQ-{RequestId}] No service endpoints available", requestId);
                return new()
                {
                    Exception = new("Failed to fetch service endpoint.")
                };
            }

            var ticket = GetTicket();
            var service = _endpoint.GetService("mspwebservice");
            var (method, isTicketRequired, _) = config.IsCustom ? (config.Method, config.TicketRequired, default) : config.Method!.GetAttribute();

            _logger.LogDebug("🎫 [REQ-{RequestId}] Ticket required: {TicketRequired}, Has ticket: {HasTicket}", requestId, isTicketRequired, ticket != null);

            if (isTicketRequired && ticket is null)
            {
                _logger.LogWarning("⚠️ [REQ-{RequestId}] Missing required ticket for method '{Method}'", requestId, method);
                return new()
                {
                    Exception = new($"A ticket is required for the method '{method}'.")
                };
            }

            if (string.IsNullOrEmpty(method))
            {
                _logger.LogError("❌ [REQ-{RequestId}] Method name is empty or null", requestId);
                return new()
                {
                    Exception = new($"Method name is required for this request.")
                };
            }

            if (isTicketRequired)
            {
                _logger.LogDebug("🎫 [REQ-{RequestId}] Using Ticket: {TicketId}", requestId, ticket.Ticket);
                config.Data = config.Data?.Prepend(ticket).ToArray();
            }
            
            var hash = AMFHash.HashContent(config.Data, isTicketRequired);
            var dataCount = config.Data?.Length ?? 0;
            
            if (config.Proxy is { })
                _logger.LogDebug("🌐 [REQ-{RequestId}] Using proxy {ProxyAddress} for method {Method}", requestId, config.Proxy.Address, method);

            _logger.LogDebug("🔐 [REQ-{RequestId}] Checksum: {Hash}, Data items: {DataCount}", requestId, hash, dataCount);
            _logger.LogInformation("📤 [REQ-{RequestId}] Sending to {Endpoint} (v{Version}) - Method: {Method}", requestId, service.Endpoint, service?.Version, method);

            var header = new List<AMFHeader>
            {
                new("sessionID", false, _hashId),
                new("id", false, hash),
                new("needClassName", false, true)
            };

            using HttpContent httpContent = new ByteArrayContent(new AMFSerializer(AMFBuilder.Encode<T>(header, service?.Endpoint?.Build(method), config.Data!)).GetData());
            httpContent.Headers.ContentType = new("application/x-amf");
            httpContent.Headers.TryAddWithoutValidation("X-Flash-Version", "32,0,0,100");

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            _logger.LogDebug("⏱️ [REQ-{RequestId}] HTTP request starting...", requestId);

            var httpResult = await Http.TrySendAsync(a =>
            {
                a.AllowOCSP();
                a.Proxy = config.Proxy;
                a.CancellationToken = config.CancellationToken;
                a.Method = HttpMethod.Post;
                a.Url = service?.Endpoint?.Build(method);
                a.Content = httpContent;
                a.Referer = "app:/cache/t1.bin/[[DYNAMIC]]/2/[[DYNAMIC]]/3";
                a.Encoding = "gzip, deflate";
                a.Accept = "text/xml, application/xml, application/xhtml+xml, text/html;q=0.9, text/plain;q=0.8, text/css, image/png, image/jpeg, image/gif;q=0.8, application/x-shockwave-flash, video/mp4;q=0.9, flv-application/octet-stream;q=0.8, video/x-flv;q=0.7, audio/mp4, application/futuresplash, */*;q=0.5, application/x-mpegURL";
                a.Agent = "Mozilla/5.0 (Windows; U; en) AppleWebKit/533.19.4 (KHTML, like Gecko) AdobeAIR/32.0";
            });

            stopwatch.Stop();
            _logger.LogDebug("⏱️ [REQ-{RequestId}] HTTP request completed in {ElapsedMs}ms - Status: {StatusCode}", requestId, stopwatch.ElapsedMilliseconds, httpResult.StatusCode);

            var stream = await httpResult.TryGetStreamAsync();
            if (!httpResult.IsSuccess || stream is null)
            {
                _logger.LogError("❌ [REQ-{RequestId}] HTTP request failed - Status: {StatusCode}, Error: {Error}", requestId, httpResult.StatusCode, httpResult.Exception?.Message ?? httpResult.Body);
                return new()
                {
                    Response = httpResult.Response,
                    Request = httpResult.Request,
                    Exception = new($"Request failed for method '{config.Method}'. Status code: {httpResult.StatusCode}, Error: {httpResult.Exception?.Message ?? httpResult.Body}")
                };
            }

            _logger.LogDebug("🔄 [REQ-{RequestId}] Decoding AMF response...", requestId);
            var decodeStopwatch = System.Diagnostics.Stopwatch.StartNew();
            var decode = await AMFBuilder.DecodeAsync(stream);
            decodeStopwatch.Stop();
            
            _logger.LogDebug("🔄 [REQ-{RequestId}] AMF decode completed in {ElapsedMs}ms - HasContent: {HasContent}, HasValue: {HasValue}, HasException: {HasException}", 
                requestId, decodeStopwatch.ElapsedMilliseconds, decode.Content != null, decode.HasValue, decode.HasException);

            if (decode.Content is null && !decode.HasValue)
            {
                _logger.LogInformation("✅ [REQ-{RequestId}] Request completed successfully with no content", requestId);
                return new()
                {
                    Response = httpResult.Response,
                    Request = httpResult.Request,
                    Exception = new($"Request for method '{config.Method}' was sent successfully, but no content was received."),
                    Success = true
                };
            }

            if (decode.HasException)
            {
                var errorContent = decode.TryReadRawContent();
                _logger.LogError("❌ [REQ-{RequestId}] AMF decoding failed: {Error}", requestId, errorContent);
                
                // Check if this is an onStatus error (server-side error)
                if (errorContent?.Contains("onStatus") == true)
                {
                    _logger.LogWarning("⚠️ [REQ-{RequestId}] Server returned onStatus error - this might be a server-side issue, not obfuscation", requestId);
                    return new()
                    {
                        Response = httpResult.Response,
                        Request = httpResult.Request,
                        Exception = new($"Server returned error for method '{config.Method}': {errorContent}"),
                        Success = false
                    };
                }
                
                return new()
                {
                    Response = httpResult.Response,
                    Request = httpResult.Request,
                    Exception = new($"Decoding failed for method '{config.Method}'. Error: {errorContent}"),
                    Success = false
                };
            }

            var json = JsonConvert.SerializeObject(decode.Content, new JsonSerializerSettings { Formatting = _client.Config.IsJsonIntend ? Formatting.Indented : Formatting.None, ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
            
            if (_logger.IsEnabled(LogLevel.Trace))
                _logger.LogTrace("📄 [REQ-{RequestId}] Response content: {Content}", requestId, json);

            _logger.LogInformation("✅ [REQ-{RequestId}] Request succeeded - Method: {Method}, Response size: {ResponseSize} chars, Total time: {TotalMs}ms", 
                requestId, method, json.Length, stopwatch.ElapsedMilliseconds);
                
            return new()
            {
                Result = JsonConvert.DeserializeObject<T>(json),
                Response = httpResult.Response,
                Request = httpResult.Request,
                Exception = null,
                Success = true,
                Json = json
            };
        }
        catch (Exception e)
        {
            _logger.LogCritical("💥 [REQ-{RequestId}] Unhandled exception in method '{Method}': {ErrorMessage}", requestId, config.Method, e.Message);
            _logger.LogDebug("💥 [REQ-{RequestId}] Exception details: {Exception}", requestId, e);

            return new()
            {
                Exception = new($"An error occurred while processing the request for method '{config.Method}'. {e.Message}", e)
            };
        }
    }
}
