using Dolo.Bot.Shop.Hub.Ticket;
using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Shop;
using DSharpPlus.EventArgs;
using MongoDB.Driver;
namespace Dolo.Bot.Shop.Hub.Ticket.Interaction;

public static class ShopOptionSelect
{
    public static async Task InvokeShopOptionAsync(this ComponentInteractionCreatedEventArgs e)
    {
        await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredChannelMessageWithSource, new DiscordInteractionResponseBuilder().AsEphemeral(true));
        var followup = await e.Interaction.TryCreateFollowupMessageAsync("Please wait ..");
        if (followup is null)
            return;

        var ticket = await TicketDatabase.GetTicketAsync(e.User.Id);
        if (ticket is not null) {
            await e.Interaction.TryEditFollowupMessageAsync(followup.Id, "You already have an open ticket.");
            return;
        }

        var followUpMessge = await e.Interaction.GetFollowupMessageAsync(e.Message.Id);
        await e.Interaction.TryDeleteFollowupMessageAsync(followUpMessge.Id);

        var choice = e.Interaction.Data.Values[0];
        if (!Hub.Guild!.TryGetMember(e.User.Id, out var member))
            return;

        var ticketChannel = await TicketCreate.TryCreateTicketChannelAsync(e, choice.GetTypeViaChoice());
        if (ticketChannel is null)
            return;

        await e.Interaction.TryEditFollowupMessageAsync(followup.Id, $"Please see in {ticketChannel.Channel!.Mention}");
        await ticketChannel.Channel!.TrySendMessageAsync(new DiscordMessageBuilder()
        .AddActionRowComponent(new DiscordButtonComponent(DiscordButtonStyle.Success, "pluto-shop-agree", "I agree", false, new DiscordComponentEmoji("✅")))
        .WithAllowedMention(UserMention.All)
        .WithContent($"""
                Hey there {e.User.Mention},

                Before purchasing any of our products, we want to remind you that all sales are final
                and non-refundable. Once you purchase any tools, it cannot be returned or
                exchanged for any reason.

                Thank you for your understanding and we hope you enjoy your purchase.
                """));
    }
}