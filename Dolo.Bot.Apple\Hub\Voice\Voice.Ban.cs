﻿using Dolo.Bot.Apple.Hub.Voice.Extension;
using Dolo.Database;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Bot.Apple;

namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("ban")]

[Description("ban a user out of the voice channel")]
    public async Task VoiceBanAsync(SlashCommandContext ctx, [Description("the user which should be banned")] DiscordUser user)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (await ctx.IsVoiceChannelAsync())
            return;

        // check if the user is a member of the server
        if (!Hub.Guild.TryGetMember(user.Id, out var member))
        {
            await ctx.TryEditResponseAsync("The user is not a server member.");
            return;
        }

        // check if the user is in the voice channel
        if (!await ctx.IsMemberInVoiceChannelAsync(member))
            return;

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the user is a admin
        if (await ctx.IsMemberAdminAsync(member))
        {
            await ctx.TryEditResponseAsync("The user is an admin and cannot be banned.");
            return;
        }

        // check if the user is a moderator
        if (usr.Moderator.Contains(user.Id))
        {
            await ctx.TryEditResponseAsync("The user is an moderator and cannot be banned.");
            return;
        }

        // check if the user is the owner
        if (usr.Owner == user.Id || member.Roles.Contains(HubRoles.Admin))
        {
            await ctx.TryEditResponseAsync("The user is the owner and cannot be banned.");
            return;
        }

        // check if the user is already banned
        if (usr.Banned.Contains(user.Id))
        {
            await ctx.TryEditResponseAsync("The user is already banned.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id) && !member.Roles.Contains(HubRoles.Admin))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // add banned user into database
        usr.Banned.Add(user.Id);

        // update the database entry with builders
        await Mongo.Voice.UpdateAsync(Builders<VoiceUser>.Filter.Eq(a => a.TextChannel, ctx.Channel.Id), Builders<VoiceUser>.Update.Set(a => a.Banned, usr.Banned));

        // kick the user
        await member.TryModifyAsync(a => a.VoiceChannel = null);

        // send the message
        await ctx.TryEditResponseAsync($"{user.Mention} has been banned from the voice channel.");
    }
}