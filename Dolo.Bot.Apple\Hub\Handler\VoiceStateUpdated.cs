﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class VoiceStateUpdated
{
    public static async Task InvokeAsync(this VoiceStateUpdatedEventArgs e)
    {
        if (Hub.Guild is null
            || HubChannel.Voice is null
            || HubChannel.VoiceTopic is null)
            return;

        var guild = await e.GetGuildAsync();
        var user = await e.GetUserAsync();
        var channel = await e.GetChannelAsync();
        DiscordChannel? afterChannel = null;
        if (e.After != null)
        {
            afterChannel = await e.After.GetChannelAsync();
            if (afterChannel != null && afterChannel.Parent != HubChannel.VoiceTopic)
                return;
        }

        if(channel != null && channel.Parent != HubChannel.VoiceTopic)
            return;
        
        var beforeChannel = await e.Before.GetChannelAsync();
        
        
        
        // invoked when the user joined the create channel
        if (channel?.Id == HubChannel.Voice.Id)
            await UserJoinedCreateChannel.InvokeAsync(new(channel, user, guild));

        // invoked when the user left the voice channel and it was empty
        if (beforeChannel != null && beforeChannel.Id != HubChannel.Voice.Id && !beforeChannel.Users.Any())
            await UserLeftVoiceEmpty.InvokeAsync(new(beforeChannel, user, guild));

        // invoked when the user joined the voice channel
        if (afterChannel != null && afterChannel.Id != HubChannel.Voice.Id)
            await UserJoinedVoice.InvokeAsync(new(channel, user, guild));

        // invoked when the user left the voice channel
        if (e.Before                     != null
            && e.Before.IsSelfMuted      == e.After?.IsSelfMuted
            && e.Before.IsSelfDeafened   == e.After?.IsSelfDeafened
            && e.Before.IsSelfStream     == e.After?.IsSelfStream
            && e.Before.IsSelfVideo      == e.After?.IsSelfVideo
            && e.Before.IsServerDeafened == e.After?.IsServerDeafened
            && e.Before.IsServerMuted    == e.After?.IsServerMuted
            && e.Before.IsSuppressed     == e.After?.IsSuppressed)
            await UserLeftVoice.InvokeAsync(new(beforeChannel, user, guild));
    }
}