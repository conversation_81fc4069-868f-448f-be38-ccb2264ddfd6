using Dolo.Core.Discord;
using Dolo.Core.Extension;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Interaction;

public static class MyInvitesPressed
{
    public static async Task InvokeMyInvitesAsync(this ComponentInteractionCreatedEventArgs args)
    {
        if (Hub.MemberSearchSystem.Members.Count == 0)
        {
            await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.ChannelMessageWithSource, new DiscordInteractionResponseBuilder()
                .AsEphemeral()
                .WithContent("Invites are being loaded, please wait a moment."));
            return;
        }

        var invites = Hub.MemberSearchSystem.GetInviteCount(args.User.Id);
        var invitesFiltered = Hub.MemberSearchSystem.GetInviteCountFiltered(args.User.Id);
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.ChannelMessageWithSource,
        new DiscordInteractionResponseBuilder()
            .AsEphemeral()
            .WithContent($"- You have **{invites}** {"invite".Pluralize(invites)}\n"
                         + "- Some of your invites could be filtered out to prevent abuse."));
    }
}