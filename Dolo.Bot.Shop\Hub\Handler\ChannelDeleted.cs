using Dolo.Bot.Shop.Hub.Ticket.Extension;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Handler;

public static class ChannelDeleted
{
    public static async Task InvokeAsync(this ChannelDeletedEventArgs e)
    {
        var db = await TicketDatabase.GetTicketFromChannelAsync(e.Channel.Id);
        if (db is null)
            return;

        await TicketDatabase.DeleteManyTicketAsync(db.UserId);
    }
}