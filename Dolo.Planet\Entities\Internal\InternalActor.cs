namespace Dolo.Planet.Entities.Internal;

internal class InternalActor
{
    [JsonProperty("NebulaProfileId", NullValueHandling = NullValueHandling.Ignore)]
    public string? NebulaProfileId { get; set; }

    [JsonProperty("ActorId", NullValueHandling = NullValueHandling.Ignore)]
    public int ActorId { get; set; }

    [JsonProperty("Name", NullValueHandling = NullValueHandling.Ignore)]
    public string? Name { get; set; }

    [JsonProperty("Level", NullValueHandling = NullValueHandling.Ignore)]
    public int Level { get; set; }

    [JsonProperty("SkinSWF", NullValueHandling = NullValueHandling.Ignore)]
    public string? SkinSwf { get; set; }

    [JsonProperty("SkinColor", NullValueHandling = NullValueHandling.Ignore)]
    public object? SkinColor { get; set; }

    [JsonProperty("NoseId", NullValueHandling = NullValueHandling.Ignore)]
    public int NoseId { get; set; }

    [JsonProperty("EyeId", NullValueHandling = NullValueHandling.Ignore)]
    public int EyeId { get; set; }

    [JsonProperty("MouthId", NullValueHandling = NullValueHandling.Ignore)]
    public int MouthId { get; set; }

    [JsonProperty("Money", NullValueHandling = NullValueHandling.Ignore)]
    public object? Money { get; set; }

    [JsonProperty("EyeColors", NullValueHandling = NullValueHandling.Ignore)]
    public string? EyeColors { get; set; }

    [JsonProperty("MouthColors", NullValueHandling = NullValueHandling.Ignore)]
    public string? MouthColors { get; set; }

    [JsonProperty("Fame", NullValueHandling = NullValueHandling.Include)]
    public long? Fame { get; set; }

    [JsonProperty("FriendCount", NullValueHandling = NullValueHandling.Ignore)]
    public int FriendCount { get; set; }

    [JsonProperty("IsExtra", NullValueHandling = NullValueHandling.Ignore)]
    public int IsExtra { get; set; }

    [JsonProperty("MembershipTimeoutDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime MembershipTimeoutDate { get; set; }

    [JsonProperty("TotalVipDays", NullValueHandling = NullValueHandling.Ignore)]
    public int TotalVipDays { get; set; }

    [JsonProperty("FriendCountVIP", NullValueHandling = NullValueHandling.Ignore)]
    public int FriendCountVip { get; set; }

    [JsonProperty("Diamonds", NullValueHandling = NullValueHandling.Ignore)]
    public object? Diamonds { get; set; }

    [JsonProperty("PopUpStyleId", NullValueHandling = NullValueHandling.Ignore)]
    public object? PopUpStyleId { get; set; }

    [JsonProperty("Moderator", NullValueHandling = NullValueHandling.Ignore)]
    public int Moderator { get; set; }

    [JsonProperty("VipTier", NullValueHandling = NullValueHandling.Ignore)]
    public int VipTier { get; set; }

    [JsonProperty("EyeShadowId", NullValueHandling = NullValueHandling.Ignore)]
    public int EyeShadowId { get; set; }

    [JsonProperty("EyeShadowColors", NullValueHandling = NullValueHandling.Ignore)]
    public string? EyeShadowColors { get; set; }

    [JsonProperty("LastLogin", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastLogin { get; set; }

    [JsonProperty("MembershipPurchasedDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime MembershipPurchasedDate { get; set; }

    [JsonProperty("Fortune", NullValueHandling = NullValueHandling.Ignore)]
    public ulong Fortune { get; set; }

    [JsonProperty("BoyfriendId", NullValueHandling = NullValueHandling.Ignore)]
    public int BoyfriendId { get; set; }

    [JsonProperty("BoyfriendStatus", NullValueHandling = NullValueHandling.Ignore)]
    public int BoyfriendStatus { get; set; }

    [JsonProperty("ActorClothesRels", NullValueHandling = NullValueHandling.Ignore)]
    public InternalActorClothesRel[]? ActorClothesRels { get; set; }

    [JsonProperty("ActorBeautyClinicItemRels", NullValueHandling = NullValueHandling.Ignore)]
    public object[]? ActorBeautyClinicItemRels { get; set; }

    [JsonProperty("Nose", NullValueHandling = NullValueHandling.Ignore)]
    public InternalNose? Nose { get; set; }

    [JsonProperty("Mouth", NullValueHandling = NullValueHandling.Ignore)]
    public InternalMouth? Mouth { get; set; }

    [JsonProperty("EyeShadow", NullValueHandling = NullValueHandling.Ignore)]
    public InternalEyeShadow? EyeShadow { get; set; }

    [JsonProperty("Eye", NullValueHandling = NullValueHandling.Ignore)]
    public InternalEye? Eye { get; set; }
}

internal class InternalActorClothesRel
{
    [JsonProperty("ActorClothesRelId", NullValueHandling = NullValueHandling.Ignore)]
    public ulong ActorClothesRelId { get; set; }

    [JsonProperty("ActorId", NullValueHandling = NullValueHandling.Ignore)]
    public int ActorId { get; set; }

    [JsonProperty("ClothesId", NullValueHandling = NullValueHandling.Ignore)]
    public int ClothesId { get; set; }

    [JsonProperty("Color", NullValueHandling = NullValueHandling.Ignore)]
    public string? Color { get; set; }

    [JsonProperty("IsWearing", NullValueHandling = NullValueHandling.Ignore)]
    public int IsWearing { get; set; }

    [JsonProperty("x", NullValueHandling = NullValueHandling.Ignore)]
    public int X { get; set; }

    [JsonProperty("y", NullValueHandling = NullValueHandling.Ignore)]
    public int Y { get; set; }

    [JsonProperty("DesignId", NullValueHandling = NullValueHandling.Ignore)]
    public int DesignId { get; set; }

    [JsonProperty("Cloth", NullValueHandling = NullValueHandling.Ignore)]
    public InternalCloth? Cloth { get; set; }

    [JsonProperty("Design", NullValueHandling = NullValueHandling.Ignore)]
    public object? Design { get; set; }

    [JsonProperty("AMF_CLASSNAME", NullValueHandling = NullValueHandling.Ignore)]
    public string? AmfClassname { get; set; }
}

internal class InternalCloth
{
    [JsonProperty("ClothesId", NullValueHandling = NullValueHandling.Ignore)]
    public int ClothesId { get; set; }

    [JsonProperty("Name", NullValueHandling = NullValueHandling.Ignore)]
    public string? Name { get; set; }

    [JsonProperty("SWF", NullValueHandling = NullValueHandling.Ignore)]
    public string? Swf { get; set; }

    [JsonProperty("ClothesCategoryId", NullValueHandling = NullValueHandling.Ignore)]
    public int ClothesCategoryId { get; set; }

    [JsonProperty("Price", NullValueHandling = NullValueHandling.Ignore)]
    public int Price { get; set; }

    [JsonProperty("ShopId", NullValueHandling = NullValueHandling.Ignore)]
    public int ShopId { get; set; }

    [JsonProperty("SkinId", NullValueHandling = NullValueHandling.Ignore)]
    public int SkinId { get; set; }

    [JsonProperty("Filename", NullValueHandling = NullValueHandling.Ignore)]
    public string? Filename { get; set; }

    [JsonProperty("Scale", NullValueHandling = NullValueHandling.Ignore)]
    public double Scale { get; set; }

    [JsonProperty("Vip", NullValueHandling = NullValueHandling.Ignore)]
    public int Vip { get; set; }

    [JsonProperty("RegNewUser", NullValueHandling = NullValueHandling.Ignore)]
    public int RegNewUser { get; set; }

    [JsonProperty("sortorder", NullValueHandling = NullValueHandling.Ignore)]
    public object? Sortorder { get; set; }

    [JsonProperty("isNew", NullValueHandling = NullValueHandling.Ignore)]
    public int IsNew { get; set; }

    [JsonProperty("Discount", NullValueHandling = NullValueHandling.Ignore)]
    public int Discount { get; set; }

    [JsonProperty("MouseAction", NullValueHandling = NullValueHandling.Ignore)]
    public int MouseAction { get; set; }

    [JsonProperty("ThemeId", NullValueHandling = NullValueHandling.Ignore)]
    public int ThemeId { get; set; }

    [JsonProperty("DiamondsPrice", NullValueHandling = NullValueHandling.Ignore)]
    public int DiamondsPrice { get; set; }

    [JsonProperty("AvailableUntil", NullValueHandling = NullValueHandling.Ignore)]
    public object? AvailableUntil { get; set; }

    [JsonProperty("ColorScheme", NullValueHandling = NullValueHandling.Ignore)]
    public string? ColorScheme { get; set; }

    [JsonProperty("LastUpdated", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastUpdated { get; set; }

    [JsonProperty("ClothesCategory", NullValueHandling = NullValueHandling.Ignore)]
    public InternalClothesCategory? ClothesCategory { get; set; }

    [JsonProperty("ThemeItem", NullValueHandling = NullValueHandling.Ignore)]
    public int ThemeItem { get; set; }

    [JsonProperty("AMF_CLASSNAME", NullValueHandling = NullValueHandling.Ignore)]
    public string? AmfClassname { get; set; }
}

internal class InternalClothesCategory
{
    [JsonProperty("ClothesCategoryId", NullValueHandling = NullValueHandling.Ignore)]
    public int ClothesCategoryId { get; set; }

    [JsonProperty("Name", NullValueHandling = NullValueHandling.Ignore)]
    public string? Name { get; set; }

    [JsonProperty("SlotTypeId", NullValueHandling = NullValueHandling.Ignore)]
    public int SlotTypeId { get; set; }

    [JsonProperty("SlotType", NullValueHandling = NullValueHandling.Ignore)]
    public object? SlotType { get; set; }

    [JsonProperty("AMF_CLASSNAME", NullValueHandling = NullValueHandling.Ignore)]
    public string? AmfClassname { get; set; }
}

internal class InternalEye : InternalBeautyFace
{
    [JsonProperty("EyeId", NullValueHandling = NullValueHandling.Ignore)]
    public int Id { get; set; }
}

internal class InternalMouth : InternalBeautyFace
{
    [JsonProperty("MouthId", NullValueHandling = NullValueHandling.Ignore)]
    public int Id { get; set; }
}

internal class InternalNose : InternalBeautyFace
{
    [JsonProperty("NoseId", NullValueHandling = NullValueHandling.Ignore)]
    public int Id { get; set; }
}

internal class InternalEyeShadow : InternalBeautyFace
{
    [JsonProperty("EyeShadowId", NullValueHandling = NullValueHandling.Ignore)]
    public int Id { get; set; }
}

internal class InternalBeautyFace
{

    [JsonProperty("Name", NullValueHandling = NullValueHandling.Ignore)]
    public string? Name { get; set; }

    [JsonProperty("SWF", NullValueHandling = NullValueHandling.Ignore)]
    public string? Swf { get; set; }

    [JsonProperty("SkinId", NullValueHandling = NullValueHandling.Ignore)]
    public int SkinId { get; set; }

    [JsonProperty("Price", NullValueHandling = NullValueHandling.Ignore)]
    public int Price { get; set; }

    [JsonProperty("Vip", NullValueHandling = NullValueHandling.Ignore)]
    public int Vip { get; set; }

    [JsonProperty("RegNewUser", NullValueHandling = NullValueHandling.Ignore)]
    public int RegNewUser { get; set; }

    [JsonProperty("sortorder", NullValueHandling = NullValueHandling.Ignore)]
    public object? Sortorder { get; set; }

    [JsonProperty("isNew", NullValueHandling = NullValueHandling.Ignore)]
    public int IsNew { get; set; }

    [JsonProperty("Discount", NullValueHandling = NullValueHandling.Ignore)]
    public int Discount { get; set; }

    [JsonProperty("ThemeID", NullValueHandling = NullValueHandling.Ignore)]
    public int ThemeId { get; set; }

    [JsonProperty("DiamondsPrice", NullValueHandling = NullValueHandling.Ignore)]
    public int DiamondsPrice { get; set; }

    [JsonProperty("DragonBone", NullValueHandling = NullValueHandling.Ignore)]
    public bool DragonBone { get; set; }

    [JsonProperty("DefaultColors", NullValueHandling = NullValueHandling.Ignore)]
    public string? DefaultColors { get; set; }

    [JsonProperty("hidden", NullValueHandling = NullValueHandling.Ignore)]
    public bool Hidden { get; set; }

    [JsonProperty("lastNewTagDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastNewTagDate { get; set; }

    [JsonProperty("LastUpdated", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastUpdated { get; set; }

    [JsonProperty("AMF_CLASSNAME", NullValueHandling = NullValueHandling.Ignore)]
    public string? AmfClassname { get; set; }

    [JsonProperty("MouthId", NullValueHandling = NullValueHandling.Ignore)]
    public int MouthId { get; set; }

    [JsonProperty("NoseId", NullValueHandling = NullValueHandling.Ignore)]
    public int NoseId { get; set; }
}