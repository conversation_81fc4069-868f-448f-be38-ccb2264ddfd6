using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Shop;
using MongoDB.Driver;
namespace Dolo.Bot.Shop.Hub.Ticket;

public partial class Ticket
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("update")]

[Description("update the ticket state")]
    public async Task UpdateAsync(SlashCommandContext ctx, [Description("the ticket state")] ShopTicketState state)
    {
        await ctx.TryDeferAsync(true);

        var db = await TicketDatabase.GetTicketFromChannelAsync(ctx.Channel.Id);
        if (db is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} User not available in our database");
            return;
        }

        if (db.State == state)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} Ticket state is already set to **{state}**");
            return;
        }

        if (!Hub.Guild!.TryGetMember(db.User<PERSON>d, out var member))
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} User not available in our database");
            return;
        }

        if (state == ShopTicketState.Open)
        {
            await ctx.Channel.ModifyAsync(a => a.Parent = HubChannel.TicketOpenCategory!);
            await ctx.Channel.AddOverwriteAsync(member, ctx.Channel.PermissionsFor(member)
                .Toggle(DiscordPermission.ViewChannel)
                .Toggle(DiscordPermission.SendMessages));
        }

        if (state == ShopTicketState.Completed)
        {
            var archive = await GetArchiveCategoryAsync();
            await TicketDatabase.DeleteTicketAsync(db.UserId);
            await ctx.Channel.ModifyAsync(a => {
                a.Parent = archive;
                a.PermissionOverwrites = [
                    new DiscordOverwriteBuilder(ctx.Guild.EveryoneRole)
                        .Deny(DiscordPermission.ViewChannel),
                    new DiscordOverwriteBuilder(member)
                        .Deny(DiscordPermission.ViewChannel)
                        .Deny(DiscordPermission.SendMessages)
                ];
            });
            await ctx.Channel.TrySendMessageAsync($"{HubEmoji.MspHeart} Ticket has been completed");
            await ctx.TryDeleteResponseAsync();
            return;
        }

        if (state == ShopTicketState.Paid) {
            await member.TryGrantRoleAsync(HubRole.Purchased);
            if(db.Type == ShopTicketType.Autograph)
                await member.TryGrantRoleAsync(HubRole.Autograph);
        }

        await Mongo.ShopTicket.UpdateAsync(Builders<ShopTicketMember>.Filter.Eq(a => a.ChannelId, ctx.Channel.Id),
        Builders<ShopTicketMember>.Update.Set(a => a.State, state));

        await ctx.Channel.TrySendMessageAsync($"{HubEmoji.MspHeart} Ticket has been updated to **{state}**");
        await ctx.TryDeleteResponseAsync();
    }
}
