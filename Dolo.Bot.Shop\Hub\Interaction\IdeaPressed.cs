using Dolo.Core.Discord;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Interaction;

public static class IdeaPressed
{
    public static async Task InvokeIdeaAsync(this ComponentInteractionCreatedEventArgs e) =>
        await e.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.Modal, new DiscordInteractionResponseBuilder()
            .WithTitle("Idea")
            .WithCustomId("idea-modal")
            .WithContent("Give us an idea for the shop")
            .AddTextInputComponent(new DiscordTextInputComponent("Idea", "idea-input", "I want to have ... in the shop", null, true, DiscordTextInputStyle.Paragraph)));
}