using Dolo.Core.Discord;
using Dolo.Database;
namespace Dolo.Bot.Shop.Hub.Ticket;

public partial class Ticket
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("delete")]

[Description("delete the ticket")]
    public async Task DeleteAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);

        var ch = ctx.Channel;
        var db = await Mongo.ShopTicket.GetOneAsync(a => a.ChannelId == ch.Id);

        if (db is null)
        {
            await ctx.Channel.TryDeleteAsync();
            return;
        }

        await Mongo.ShopTicket.DeleteAsync(a => a.ChannelId == ch.Id);
        await ctx.Channel.TryDeleteAsync();
    }
}