using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using Dolo.Pluto.Shard.Bot.Shop;
using DSharpPlus.EventArgs;
#pragma warning disable CS8604// Possible null reference argument.
namespace Dolo.Bot.Shop.Hub.Ticket.Interaction;

public static class ShopQuestionPressed
{
    public static async Task InvokeShopQuestionAsync(this ComponentInteractionCreatedEventArgs args)
    {
        // Defer the response to give us time to process the request
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredChannelMessageWithSource, new DiscordInteractionResponseBuilder().AsEphemeral(true));

        // Send an initial "loading" message that we'll update later
        var followupMessage = await args.Interaction.TryCreateFollowupMessageAsync("Please wait...");

        // Check for existing ticket - if user has valid ticket, method handles the response
        if (await args.HasValidExistingTicketAsync(followupMessage!.Id))
            return;

        // No existing ticket found, show the product selection menu
        var ticketEntity = await args.TryCreateTicketChannelAsync(ShopTicketType.Question);
        if (ticketEntity is null)
        {
            await args.Interaction.TryEditFollowupMessageAsync(followupMessage!.Id, "Please try again later ..");
            return;
        }


        await ticketEntity.Channel!.TrySendMessageAsync($"Hey there {args.User.Mention}, please ask your question here.");
        await args.Interaction.TryEditFollowupMessageAsync(followupMessage!.Id, $"Please see in {ticketEntity.Channel!.Mention}");
    }
}
