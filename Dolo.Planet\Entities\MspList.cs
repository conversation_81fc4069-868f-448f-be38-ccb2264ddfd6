namespace Dolo.Planet.Entities;

public sealed class MspList<T> : List<T>
{
    public bool Success { internal set; get; }
    public bool IsServiceUnavailable => HttpResponse?.StatusCode is HttpStatusCode.ServiceUnavailable or HttpStatusCode.InternalServerError;
    public bool IsRateLimited => Status?.Contains("RateLimited") ?? false;
    public string? Status => HttpException?.Message;
    public HttpRequestMessage? HttpRequest { internal set; get; }
    public HttpResponseMessage? HttpResponse { internal set; get; }
    public HttpRequestException? HttpException { internal set; get; }
    public string? GetStatusCode() => HttpResponse is {} ?
                                          HttpResponse.StatusCode.ToString() :
                                          HttpException is {} ?
                                              HttpException.StatusCode.ToString() :
                                              "Unknown error";
}