﻿namespace Dolo.Bot.Apple.Hub.Interaction;

public static class Welcome
{
    public static async Task HandleWelcomeAsync(this ComponentInteractionCreatedEventArgs args)
    {
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredMessageUpdate);

        if (args.Values.FirstOrDefault() == "question-pluto")
            await args.QuestionPlutoAppAsync();

        if (args.Values.FirstOrDefault() == "question-pluto-verify")
            await args.QuestionPlutoVerificationAsync();

        if (args.Values.FirstOrDefault() == "question-pluto-download")
            await args.QuestionPlutoDownloadAsync();
    }

    private static async Task QuestionPlutoAppAsync(this InteractionCreatedEventArgs args) => await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                                                                                             .AddEmbed(HubEmbed.InteractionQuestionPluto().Embeds[0])
                                                                                             .AddActionRowComponent((DiscordActionRowComponent)HubEmbed.InteractionQuestionPluto().Components[0])
                                                                                             .AsEphemeral());

    private static async Task QuestionPlutoVerificationAsync(this InteractionCreatedEventArgs args) => await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                                                                                                       .AddEmbed(HubEmbed.InteractionQuestionPlutoVerify().Embeds[0])
                                                                                                       .AddActionRowComponent((DiscordActionRowComponent)HubEmbed.InteractionQuestionPlutoVerify().Components[0])
                                                                                                       .AsEphemeral());

    private static async Task QuestionPlutoDownloadAsync(this InteractionCreatedEventArgs args) => await args.Interaction.TryCreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                                                                                                   .AddEmbed(HubEmbed.InteractionQuestionPlutoDownload().Embeds[0])
                                                                                                   .AddActionRowComponent((DiscordActionRowComponent)HubEmbed.InteractionQuestionPlutoDownload().Components[0])
                                                                                                   .AsEphemeral());
}