using Dolo.Core;
using Dolo.Core.AMF3;
using Dolo.Core.Extension;
using Dolo.Core.Http;
using Dolo.Planet.Entities;
using Dolo.Planet.Entities.Beauty;
using Dolo.Planet.Entities.Cloth;
using Dolo.Planet.Entities.Friend;
using Dolo.Planet.Entities.Game;
using Dolo.Planet.Entities.Internal;
using Dolo.Planet.Entities.Single;
using Dolo.Planet.Entities.Theme;
using Dolo.Planet.Enums;
using Dolo.Planet.NET.Entities;
using Dolo.Planet.Utils;
using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Text.RegularExpressions;

// ReSharper disable InvalidXmlDocComment

namespace Dolo.Planet.NET;

[Obfuscation(Feature = "internalization", Exclude = true)]
internal partial class MspApi
{
    internal readonly MspClient _client;
    internal ServiceEndpointList? _endpoint;
    internal string? _hashId;
    internal ILogger _logger;

    public MspApi(MspClient client)
    {
        _client = client;
        _hashId = AMFHash.HashId();
        _logger = _client.LogFactory.CreateLogger<MspApi>();
    }

    /// <summary>
    ///     Update the SessionId of the AMFHeader
    /// </summary>
    /// <param name="newsession"></param>
    public void UpdateSessionId(string? newsession)
    {
        _hashId = newsession;
    }

    /// <summary>
    ///     Method to dump all names
    /// </summary>
    /// <returns></returns>
    [SuppressMessage("ReSharper.DPA", "DPA0000: DPA issues")]
    public async Task<IEnumerable<MspName>> DumpNamesAsync()
    {
        var names = new List<MspName>();
        var tasks = new List<Task>();
        for (var i = 0; i < 100000000; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                var name = await _client.GetActorNameAsync(i);
                if (name is { Success: true, IsAvailable: true })
                    names.Add(name);
            }));
            await Task.Delay(50);
        }

        await Task.WhenAll(tasks);
        return names;
    }





    public async Task<MspResult<int>> GetFameFromRoomCompetitionAsync([CallerMemberName] string mthd = "")
    {
        var ids = new[]
        {
            0, 19, 31, 45, 14, 46, 25, 1, 173, 71, 122, 229, 385, 557, 365, 7, 593, 497, 433, 333, 738, 361, 189, 609, 613, 601, 734, 870, 649, 605, 714, 730, 633, 621, 746, 1450, 878, 1522, 1046, 1418, 1146, 1294, 1150, 942, 1082, 1314, 1736, 1034, 990, 1732, 1604, 1166, 1263, 1768, 1254, 2926, 637, 1760, 1282, 1781, 1612, 1772, 1776, 1584, 1550, 1812, 4022, 1066, 1744, 1828, 1756, 926, 1546, 1354, 1824, 1086, 1792, 1218, 1864, 1872, 4025, 1800, 1816, 1912, 1840, 1748, 1928, 4026, 1908, 1797, 1940, 2953, 1804, 1944, 2942, 2961, 2951, 2949, 2906, 1956, 2946, 1948, 1964, 1968, 2947, 1972, 2958, 1856, 1936, 1832, 1960, 2941, 1984, 3965, 1724, 2004, 1720, 2020, 2008, 3425, 2488, 2695, 2924, 3003, 2000, 3062, 3029, 1992, 1286, 2036, 2552, 2032, 3352, 3592, 1226, 2024, 3368, 2056, 2060, 2052, 3277, 2040, 2348, 2344, 2647, 2080, 4810, 292, 2044, 2096, 2068, 2925, 2084, 2600, 1996, 4372, 2088, 1615, 2124, 2120, 2128, 2132, 2720, 3387, 2116, 3155, 3519, 3376, 2072, 3159, 3370, 3366, 2144, 2156, 2741, 2172, 3113, 2180, 2934, 4019, 2968, 2148, 2168, 2940, 2196, 2208, 2962, 2204, 2963, 2188, 2192, 4565, 2879, 2212, 2224, 1980, 2228, 1632, 2220, 2244, 2248, 2240, 2256, 2264, 2965, 3143, 1616, 2252, 2276, 3157, 1884, 2232, 2284, 2288, 1704, 4240, 2296, 4206, 4103, 4104, 1689, 4221, 2304, 2300, 2312, 2316, 4984, 2320, 4958, 2200, 2340, 4960, 4601, 2328, 4091, 2308, 2272, 2292, 2364, 2280, 2324, 1880, 2376, 2236, 1618, 1624, 838, 2372, 2396, 2388, 2260, 1178, 2404, 1386, 2412,
            2416, 2268, 2392, 2424, 2548, 2652, 2644, 2661, 2679, 2689, 2524, 2709, 2630, 2627, 2636, 2642, 2635, 1406, 2745, 2747, 2754, 2760, 2692, 2805, 2686, 2708, 2712, 2824, 2611, 283, 2738, 2730, 2871, 2855, 2878, 2929, 2907, 2688, 2937, 2788, 2786, 2626, 2997, 2684, 2508, 2813, 2637, 3015, 3030, 3017, 2848, 2852, 2728, 2683, 2905, 2670, 3064, 2360, 3046, 2969, 2663, 2967, 2682, 2999, 2703, 2693, 3012, 2704, 3032, 2687, 2836, 2721, 3082, 2727, 2837, 2993, 3091, 2696, 2638, 2826, 3024, 2685, 3039, 2901, 2992, 2750, 3150, 3149, 3068, 2616, 3112, 2777, 3000, 3128, 2789, 3094, 2807, 3163, 2649, 3168, 3018, 3105, 2904, 3107, 2793, 2839, 2799, 3174, 3010, 3176, 3178, 2804, 3085, 3182, 3156, 2801, 3004, 2664, 3188, 3089, 3009, 187, 2702, 3100, 3166, 3171, 3106, 2827, 2838, 2751, 3201, 3126, 3154, 2841, 2932, 2978, 3122, 2860, 3134, 2998, 2885, 2691, 3210, 3127, 2817, 3170, 3194, 3102, 3173, 2896, 3001, 3221, 3222, 3135, 3200, 3109, 3133, 2984, 3118, 3229, 2991, 3164, 3169, 3048, 3031, 3214, 3237, 3179, 3119, 3240, 3144, 3131, 3053, 3231, 2990, 3141, 2724, 3101, 2516, 3236, 3139, 3254, 3261, 3259, 3167, 3115, 3227, 3223, 3267, 2989, 3268, 3147, 3272, 3250, 3097, 3215, 3282, 3192, 3239, 3257, 3291, 3262, 3219, 3266, 3075, 3297, 2808, 3270, 3211, 3300, 3096, 3241, 3276, 3306, 3288, 3292, 3293, 3289, 3294, 3185, 3313, 3298, 3316, 2608, 3199, 3095, 3232, 3142, 3238, 3322, 3321, 3324, 3326, 3218, 3235, 3124, 3242, 3329, 3226, 3314, 3290, 3217, 3278, 3281, 3338, 3132,
            3308, 3286, 3307, 3342, 3343, 3333, 3344, 3196, 3279, 3129, 3351, 2810, 3354, 3225, 3341, 3357, 3365, 3360, 3373, 3384, 3385, 3350, 3389, 3275, 3330, 3212, 3355, 3274, 3394, 3393, 3383, 3364, 3362, 3328, 3398, 3256, 3121, 3401, 3296, 3404, 3208, 3407, 3408, 3409, 3410, 3172, 3421, 3424, 3340, 3438, 3273, 3460, 3114, 3504, 3508, 3527, 3264, 3526, 3320, 3550, 3626, 3670, 3547, 3750, 3269, 3458, 3779, 3788, 3390, 3411, 3866, 3531, 3873, 3540, 3902, 3103, 3897, 3672, 3668, 3271, 3907, 3905, 3741, 3736, 3466, 3938, 3941, 3830, 3770, 4068, 3280, 4015, 3733, 3968, 3896, 4169, 4166, 3608, 4173, 3562, 4226, 4223, 3691, 448, 4252, 3579, 3253, 4294, 4296, 3613, 4312, 4314, 4320, 3611, 3952, 3618, 3881, 3666, 4085, 4106, 3894, 4348, 4350, 3684, 3559, 4167, 4381, 3251, 3686, 3695, 4222, 4237, 3689, 4290, 3929, 3730, 4293, 4299, 3538, 3742, 4325, 4010, 3751, 4064, 4386, 4090, 4333, 3761, 4388, 3760, 4150, 3709, 4291, 3795, 4313, 3678, 3789, 4395, 4383, 3822, 4337, 3657, 4344, 4322, 3816, 3804, 3903, 4280, 4398, 3828, 3284, 4368, 3817, 4400, 3831, 4069, 4392, 4391, 3826, 3627, 4407, 3784, 4405, 4411, 4359, 3847, 3810, 4364, 3855, 3932, 4397, 3054, 3856, 4531, 3857, 4523, 3890, 4603, 4113, 3332, 3858, 4613, 3794, 4615, 4365, 4410, 4402, 4623, 3600, 4374, 4413, 4417, 4627, 3777, 4330, 4657, 4111, 3876, 42, 4999, 4401, 3934, 4408, 4619, 4618, 4414, 4297, 3337, 4163, 4004, 4261, 4114, 4327, 4418, 4354, 4112, 4387, 113, 4614, 4995, 4370, 3405, 74, 4393, 4331, 4420, 4336,
            89, 146, 4358, 4362, 4335, 3485, 95, 157, 4626, 4321, 4082, 4990, 4609, 86, 1490, 169, 2764, 4610, 56, 4137, 3110, 185, 253, 269, 201, 193, 221, 107, 209, 241, 249, 2840, 289, 197, 273, 413, 261, 153, 277, 233, 349, 397, 481, 297, 441, 477, 285, 281, 293, 225, 449, 405, 309, 389, 357, 429, 445, 465, 325, 417, 581, 3818, 565, 301, 501, 585, 353, 421, 313, 537, 337, 525, 369, 1534, 553, 489, 485, 521, 517, 702, 561, 509, 718, 4576, 529, 4035, 742, 710, 321, 329, 726, 722, 541, 589, 341, 641, 617, 653, 3481, 645, 3529, 3806, 391, 794, 3928, 656, 573, 950, 3832, 3889, 1050, 237, 597, 1054, 381, 533, 666, 662, 549, 4477, 670, 758, 678, 4511, 802, 762, 658, 4444, 3882, 682, 4403, 149, 4369, 966, 4956, 1094, 958, 1030, 982, 786, 205, 4349, 217, 798, 1138, 28, 1041, 778, 782, 125, 1044, 1664, 978, 131, 986, 1074, 4643, 4494, 62, 4404, 830, 806, 47, 810, 790, 1106, 1026, 1307, 4967, 946, 1186, 814, 1338, 1478, 770, 4244, 902, 1114, 1038, 938, 1656, 1346, 1042, 4517, 4564, 4519, 4532, 1382, 1608, 4981, 4515, 4671, 4566, 4514, 1696, 1366, 4717, 1199, 1343, 1222, 858, 4855, 1394, 1514, 3644, 1554, 1378, 1261, 1712, 4498, 1617, 1278, 1542, 1014, 1474, 1238, 1458, 1010, 1350, 1466, 1462, 4572, 846, 818, 1426, 1322, 1530, 1215, 1506, 1362, 1595, 1414, 4478, 1562, 4946, 1182, 1422, 1162, 4497, 1110, 4724, 4906, 822, 4787, 4569, 4788, 1246, 4894, 1194, 491, 4460, 1330, 4902, 4812, 4763, 4860, 4441, 4438, 4925, 4908, 4871, 4857, 4950, 4883, 4863, 4803, 4886, 4868, 4888,
            4881, 4466, 4869, 4861, 4879, 4904, 4913, 4847, 4851, 4450, 4853, 4852, 4893, 4844, 4831, 4895, 4919, 4836, 4689, 4683, 4892, 4660, 4647, 4804, 4666, 4652, 4843, 4634, 4826, 4849, 4738, 4705, 4640, 4784, 4918, 4681, 4780, 4885, 4630, 4685, 4649, 4637, 4667, 4767, 4661, 4653, 4628, 4842, 4825, 4633, 4834, 1764, 4890, 4778, 1860, 1820, 4454, 4761, 1784, 6, 119, 4859, 4891, 3475, 3989, 3339, 2176, 134, 1988, 4817, 2076, 92, 265, 161, 4864, 35, 26, 10, 34, 4793, 2959, 4795, 2064, 83, 4740, 11, 3, 3363, 4023, 1844, 12, 393, 4659, 177, 4629, 4940, 27, 1836, 4642, 3386, 401, 4734, 4428, 106, 59, 2160, 41, 20, 4764, 473, 43, 4732, 4799, 4703, 453, 3379, 110, 4768, 4433, 80, 4691, 65, 38, 3378, 2164, 24, 5, 3397, 4774, 181, 2152, 13, 4809, 461, 2948, 16, 317, 2136, 44, 409, 128, 4832, 305, 29, 4833, 469, 1202, 39, 4907, 457, 377, 140, 4889, 2112, 4897, 373, 2100, 4800, 15, 505, 213, 22, 4917, 257, 77, 17, 4854, 4880, 33, 4757, 2945, 2104, 437, 4588, 914, 4756, 2960, 4749, 30, 569, 4668, 23, 4911, 4910, 4459, 4955, 4971, 21, 4952, 2108, 577, 4590, 4905, 1952, 513, 4426, 4484, 4430, 754, 165, 3287, 750, 2, 4928, 2944, 4474, 2012, 68, 4973, 18, 3819, 698, 706, 3649, 3888, 4462, 4656, 4457, 4471, 4648, 4899, 137, 4632, 674, 629, 4736, 4677, 9, 625, 4686, 4586, 4472, 4682, 4464, 4662, 545, 4012, 690, 4798, 3381, 4432, 4739, 4751, 1868, 4600, 4737, 4688, 4933, 1920, 4742, 345, 4598, 4766, 493, 4830, 4790, 425, 143, 36, 101, 245, 8, 37, 2048, 3367, 98, 40, 3375, 116,
            32, 4
        };

        var room = await _client.User.Actor.GetRoomAsync();
        if (!room.Success)
            return new()
            {
                MovieStarPlanet = _client,
                Success = false,
                HttpException = room.HttpException,
                HttpRequest = room.HttpRequest,
                HttpResponse = room.HttpResponse
            };

        var res = await _client.SubmitCompetitionAsync(ids.ToList().Shuffle().First(), room.Id);

        return new()
        {
            MovieStarPlanet = _client,
            Success = res.Success,
            Value = res.Value
        };
    }


    /// <summary>
    ///     Get all bonster with highscore
    /// </summary>
    /// <param name="mthd"></param>
    /// <returns></returns>
    public async Task<MspList<MspPet>> GetBonsterWithHighscoreAsync([CallerMemberName] string mthd = "")
    {
        var req = await _client.GetHighscoreBonsterAsync(a => a.UseCount(500));
        var res = new MspList<MspPet>
        {
            HttpException = req.HttpException,

            HttpRequest = req.HttpRequest,
            HttpResponse = req.HttpResponse,
            Success = req.Success
        };

        if (!req.Success) return res;

        var req2 = await _client.GetHighscoreActorsAsync(a => a.UseCount(500));
        res.HttpException = req2.HttpException;
        res.HttpRequest = req2.HttpRequest;
        res.HttpResponse = req2.HttpResponse;
        req.Success = req2.Success;

        if (!req2.Success) return res;

        res.AddRange(req.Select(pets => new MspPet
        {
            MovieStarPlanet = _client,
            Id = pets.Id
        }));

        foreach (var usr in req2)
        {
            var rd = await usr.GetBoonstersAsync();
            if (!rd.Success)
                continue;

            res.AddRange(rd.Select(pet => new MspPet
            {
                MovieStarPlanet = _client,
                Id = pet.Id
            }));
        }

        return res;
    }



    /// <summary>
    ///     Generate a random look
    /// </summary>
    /// <returns></returns>
    public async Task<MspList<MspActorCloth>> GenerateLookAsync()
    {
        var o = await _client.GetActorClothAsync(_client.User.Actor.Id);
        var m = await _client.User.Actor.GetBeautyAsync();

        if (!o.Success) return o;

        var res = new MspList<MspActorCloth>
        {
            Success = o.Success,
            HttpException = o.HttpException,
            HttpRequest = o.HttpRequest,
            HttpResponse = o.HttpResponse
        };

        List<MspActorCloth> cloths =
            [];

        var rnd = new List<MspActorCloth>(o);
        var bty = new List<MspBeautyDataInventory>();

        var head = rnd.Where(a => a.Cloth is { IsHead: true })
            .ToList().Shuffle().FirstOrDefault();
        var accessories = rnd.Where(a => a.Cloth is { IsAccessories: true })
            .ToList().Shuffle().FirstOrDefault();
        var bottom = rnd.Where(a => a.Cloth is { IsBottom: true })
            .ToList().Shuffle().FirstOrDefault();
        var foot = rnd.Where(a => a.Cloth is { IsFoot: true })
            .ToList().Shuffle().FirstOrDefault();
        var hair = rnd.Where(a => a.Cloth is { IsHair: true })
            .ToList().Shuffle().FirstOrDefault();
        var stuff = rnd.Where(a => a.Cloth is { IsStuff: true })
            .ToList().Shuffle().FirstOrDefault();
        var top = rnd.Where(a => a.Cloth is { IsTop: true })
            .ToList().Shuffle().FirstOrDefault();
        var eyes = m.Inventory.Where(a => a.Type is BeautyType.Eyes)
            .ToList().Shuffle().FirstOrDefault();
        var eyeShadow = m.Inventory.Where(a => a.Type is BeautyType.EyeShadows)
            .ToList().Shuffle().FirstOrDefault();
        var mouth = m.Inventory.Where(a => a.Type is BeautyType.Mouths)
            .ToList().Shuffle().FirstOrDefault();
        var nose = m.Inventory.Where(a => a.Type is BeautyType.Noses)
            .ToList().Shuffle().FirstOrDefault();
        var skin = m.Inventory.Where(a => a.Type is BeautyType.Skins)
            .ToList().Shuffle().FirstOrDefault();

        if (head != null)
            cloths.Add(head);
        if (accessories != null)
            cloths.Add(accessories);
        if (bottom != null)
            cloths.Add(bottom);
        if (foot != null)
            cloths.Add(foot);
        if (hair != null)
            cloths.Add(hair);
        if (stuff != null)
            cloths.Add(stuff);
        if (top != null)
            cloths.Add(top);
        if (eyes != null)
            bty.Add(eyes);
        if (eyeShadow != null)
            bty.Add(eyeShadow);
        if (mouth != null)
            bty.Add(mouth);
        if (nose != null)
            bty.Add(nose);
        if (skin != null)
            bty.Add(skin);

        await _client.UpdateClothesAsync(cloths.Select(l => l.Id).ToArray()).ConfigureAwait(false);
        await _client.WearItemsAsync(bty.Select(a => a.Id).ToArray()).ConfigureAwait(false);

        res.AddRange(cloths);

        return res;
    }





    /// <summary>
    ///     Get the Actor
    /// </summary>
    /// <param name="name"></param>
    /// <param name="mth"></param>
    /// <returns></returns>
    public async Task<MspList<MspActor>> GetActorBulksAsync(IEnumerable<int> ids, bool withCreationDate = true, bool withStatus = true, bool processUrl = true,
        [CallerMemberName] string mth = "")
    {
        const int chunkSize = 50;

        var req = new MspList<InternalActor>();
        var chunks = ids
            .Select((x, i) => new { Index = i, Value = x })
            .GroupBy(x => x.Index / chunkSize)
            .Select(x => x.Select(v => v.Value).ToList())
            .ToList();

        var reqTasks = chunks.Select(chunk => Task.Run(async () =>
        {
            var r = await _client.Api.SendAsync<List<InternalActor>>(mth, chunk.ToArray()).ConfigureAwait(false);
            if (r is { Success: true, Result: { } })
            {
                req.HttpRequest = r.Request;
                req.HttpResponse = r.Response;
                req.HttpException = r.Exception;
                req.Success = r.Success;

                if (r.Result?.Any() ?? false)
                    req.AddRange(r.Result);
            }
        }))
            .ToList();

        await Task.WhenAll(reqTasks);
        var res = new MspList<MspActor>
        {
            HttpRequest = req.HttpRequest,
            HttpResponse = req.HttpResponse,
            HttpException = req.HttpException,
            Success = req.Success
        };

        if (!res.Success)
            return
                [];

        List<Task> tasks
            =
            [];


        foreach (var mspActor in req)
        {
            tasks.Add(_ = Task.Run(async () =>
            {
                if (mspActor.Level > 0 && (mspActor.Fortune <= 0 || (mspActor.Fame.Is<int>() ? mspActor.Fame.To<int>() <= 0 : mspActor.Fame.To<long>() <= 0)))
                    return;

                var actor = new MspActor
                {
                    Server = _client.User.Server,
                    BaseId = mspActor.ActorId,
                    ClothRel = [],
                };

                if (withStatus)
                    actor.Status = await _client.GetActorStatusAsync(mspActor.ActorId);

                mspActor.ActorClothesRels?.ToList().ForEach(a =>
                {
                    if (a.Cloth?.ClothesCategory is null) return;

                    actor.ClothRel.Add(new()
                    {
                        ActorId = a.ActorId,
                        ClothId = a.ClothesId,
                        Color = a.Color,
                        Id = a.ActorClothesRelId,
                        IsWearing = a.IsWearing != 0,
                        Cloth = new()
                        {
                            CategoryId = a.Cloth.ClothesCategoryId,
                            ColorScheme = a.Cloth.ColorScheme,
                            DiamondsPrice = a.Cloth.DiamondsPrice,
                            Discount = a.Cloth.Discount,
                            Id = a.Cloth.ClothesCategoryId,
                            IsNew = a.Cloth.IsNew != 0,
                            IsVip = a.Cloth.Vip != 0,
                            LastUpdatedAt = a.Cloth.LastUpdated,
                            ThemeId = a.Cloth.ThemeId,
                            Name = a.Cloth.Name,
                            Price = a.Cloth.Price,
                            Scale = a.Cloth.Scale,
                            ShopId = a.Cloth.ShopId,
                            SkinId = a.Cloth.SkinId,
                            Sortorder = a.Cloth.Sortorder,
                            Filename = a.Cloth.Swf,
                            SwfUrl = GetCategoryUrl(a.Cloth.ClothesCategoryId) + a.Cloth.Swf?.Replace(" ", "%20") +
                                     ".swf",
                            Category = new()
                            {
                                Id = a.Cloth.ClothesCategory.ClothesCategoryId,
                                Name = a.Cloth.ClothesCategory.Name,
                                SlotTypeId = a.Cloth.ClothesCategory.SlotTypeId
                            }
                        }
                    });
                });
                actor.MovieStarPlanet = _client;
                actor.Beauty = new()
                {
                    MovieStarPlanet = _client,
                    Skincolor = mspActor.SkinColor,
                    Eye = mspActor.Eye is null
                              ? default
                              : new MspEye
                              {
                                  Id = mspActor.Eye.Id,
                                  LastUpdatedAt = mspActor.Eye.LastUpdated,
                                  Color = mspActor.EyeColors,
                                  DefaultColors = mspActor.Eye.DefaultColors,
                                  DiamondsPrice = mspActor.Eye.DiamondsPrice,
                                  Discount = mspActor.Eye.Discount,
                                  IsDragonBone = mspActor.Eye.DragonBone,
                                  IsHidden = mspActor.Eye.Hidden,
                                  SkinId = mspActor.Eye.SkinId,
                                  IsNew = mspActor.Eye.IsNew != 0,
                                  IsVip = mspActor.Eye.Vip != 0,
                                  Price = mspActor.Eye.Price,
                                  Sortorder = mspActor.Eye.Sortorder,
                                  Swf = mspActor.Eye.Swf
                              },
                    Mouth = mspActor.Mouth is null
                                ? default
                                : new MspMouth
                                {
                                    Id = mspActor.Mouth.Id,
                                    LastUpdatedAt = mspActor.Mouth.LastUpdated,
                                    DefaultColors = mspActor.Mouth.DefaultColors,
                                    DiamondsPrice = mspActor.Mouth.DiamondsPrice,
                                    Discount = mspActor.Mouth.Discount,
                                    IsDragonBone = mspActor.Mouth.DragonBone,
                                    IsHidden = mspActor.Mouth.Hidden,
                                    SkinId = mspActor.Mouth.SkinId,
                                    IsNew = mspActor.Mouth.IsNew != 0,
                                    IsVip = mspActor.Mouth.Vip != 0,
                                    Price = mspActor.Mouth.Price,
                                    Sortorder = mspActor.Mouth.Sortorder,
                                    Swf = mspActor.Mouth.Swf,
                                    Color = mspActor.MouthColors
                                },
                    Nose = mspActor.Nose is null
                               ? default
                               : new MspNose
                               {
                                   Id = mspActor.Nose.Id,
                                   LastUpdatedAt = mspActor.Nose.LastUpdated,
                                   DefaultColors = mspActor.Nose.DefaultColors,
                                   DiamondsPrice = mspActor.Nose.DiamondsPrice,
                                   Discount = mspActor.Nose.Discount,
                                   IsDragonBone = mspActor.Nose.DragonBone,
                                   IsHidden = mspActor.Nose.Hidden,
                                   SkinId = mspActor.Nose.SkinId,
                                   IsNew = mspActor.Nose.IsNew != 0,
                                   IsVip = mspActor.Nose.Vip != 0,
                                   Price = mspActor.Nose.Price,
                                   Sortorder = mspActor.Nose.Sortorder,
                                   Swf = mspActor.Nose.Swf
                               },
                    EyeShadow = mspActor.EyeShadow is null
                                    ? null
                                    : new MspEyeShadow
                                    {
                                        Id = mspActor.EyeShadow.Id,
                                        LastUpdatedAt = mspActor.EyeShadow.LastUpdated,
                                        DefaultColors = mspActor.EyeShadow.DefaultColors,
                                        DiamondsPrice = mspActor.EyeShadow.DiamondsPrice,
                                        Discount = mspActor.EyeShadow.Discount,
                                        IsDragonBone = mspActor.EyeShadow.DragonBone,
                                        IsHidden = mspActor.EyeShadow.Hidden,
                                        SkinId = mspActor.EyeShadow.SkinId,
                                        IsNew = mspActor.EyeShadow.IsNew != 0,
                                        IsVip = mspActor.EyeShadow.Vip != 0,
                                        Price = mspActor.EyeShadow.Price,
                                        Sortorder = mspActor.EyeShadow.Sortorder,
                                        Swf = mspActor.EyeShadow.Swf,
                                        Color = mspActor.EyeShadowColors
                                    }
                };
                actor.Avatar = new()
                {
                    AvatarUrl = GetMovieStarAvatarUrl(mspActor.ActorId),
                    BodyUrl = GetMovieStarBodyUrl(mspActor.ActorId)
                };
                actor.Id = mspActor.ActorId;
                actor.CreatedAt = withCreationDate ? (await _client.GetActorSummaryAsync(actor.Id)).Created : new();
                actor.Username = mspActor.Name;
                actor.ProfileId = mspActor.NebulaProfileId;
                actor.Level = mspActor.Level;
                actor.Friends = mspActor.FriendCount;
                actor.FriendsVip = mspActor.FriendCountVip;
                actor.MembershipPurchasedAt = mspActor.MembershipPurchasedDate;
                actor.MembershipTimeoutAt = mspActor.MembershipTimeoutDate;
                actor.LastLoginAt = mspActor.LastLogin;
                actor.IsModerator = mspActor.Moderator != 0;
                actor.IsVip = DateTime.UtcNow < mspActor.MembershipTimeoutDate;
                actor.IsCeleb = mspActor.FriendCountVip >= 100;
                actor.IsJudge = mspActor.TotalVipDays >= 180;
                actor.IsJury = !(mspActor.TotalVipDays >= 180) && mspActor.TotalVipDays >= 90;
                actor.IsDeleted = mspActor.IsExtra != 0;
                actor.VipTier = !(DateTime.UtcNow < mspActor.MembershipTimeoutDate) || mspActor.VipTier == 0
                                    ? VipTierType.NON_VIP
                                    : (VipTierType)mspActor.VipTier;
                actor.Gender = mspActor.SkinSwf == "femaleskin" ? Gender.Female : Gender.Male;
                actor.StarCoins = mspActor.Money.To<int>();
                actor.Diamonds = mspActor.Diamonds.To<int>();
                actor.Fame = mspActor.Fame.Is<int>() ? mspActor.Fame.To<int>() : mspActor.Fame.To<long>();
                actor.Fortune = mspActor.Fortune.To<int>();
                actor.MaxFriends = GetMaxFriends((int)actor.VipTier, actor.Level);
                actor.RoomUrl = GetObfuscatedUrl(ObfuscationType.room, actor.Id);
                actor.BaseId = mspActor.ActorId;
                actor.FriendStatus = actor.Status.FriendStatus;


                if (processUrl)
                    await actor.Avatar.DoAvatarAvailableAsync(actor.Gender, true);

                res.Add(actor);
            }));
            await Task.Delay(1);
        }

        await Task.WhenAll(tasks);
        return res;
    }



    internal async Task SyncFetchAsync()
    {
        await _client.GetPiggyBankAsync();
        await _client.LoadActorDetailsExtendedAsync();

        await _client.Socket.DisconnectAsync();
        await _client.Socket.TryConnectAsync(_client);
    }

    /// <summary>
    ///     Scan for available sessionids
    /// </summary>
    /// <returns></returns>
    // add attribute that this is currently not necessary
    [Obsolete("This method is currently not necessary")]
    internal async Task<MspSession> ScanSessionAsync()
    {
        if (!_client.Config.IsSessionProcessing)
            return new(true);

        var hub = await MspClientHub.GetHubAsync();
        if (!hub.IsOk)
            return hub;

        var mspClient = new MspClient(a =>
        {
            a.Server = Server.UnitedStates;
        });

        for (var i = 0; i < 35; i++)
        {
            var r = await mspClient.CreateRefAsync();

            if (!r.Success || string.IsNullOrEmpty(r.Data) || string.IsNullOrEmpty(r.RefId)) continue;
            if (!Constant.MSP_SESSION_HERACHY.TryGetValue(r.Data, out var value)) continue;

            var os = await mspClient.RunOsAsync(r.RefId, value);

            if (string.IsNullOrEmpty(os.SessionId))
                return hub.IsNotOk();

            if (os is not { Success: true, HasSessionId: true })
                continue;

            UpdateSessionId(os.SessionId);
            return hub;
        }

        return hub.IsNotOk();
    }
}
