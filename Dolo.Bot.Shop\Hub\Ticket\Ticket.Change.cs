using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Shop;
using MongoDB.Driver;
namespace Dolo.Bot.Shop.Hub.Ticket;

public partial class Ticket
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("change")]
    [Description("change the ticket to another one")]
    public async Task ChangeAsync(SlashCommandContext ctx, [Description("the ticket type")] ShopTicketType type)
    {
        await ctx.TryDeferAsync(true);

        var db = await TicketDatabase.GetTicketFromChannelAsync(ctx.Channel.Id);
        if (db is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} User not available in our database");
            return;
        }

        if (db.Type == type)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} Ticket type is already set to **{type}**");
            return;
        }

        // set the new type
        db.Type = type;

        await Mongo.ShopTicket.UpdateAsync(Builders<ShopTicketMember>.Filter.Eq(a => a.ChannelId, ctx.Channel.Id),
        Builders<ShopTicketMember>.Update.Set(a => a.Type, type));

        await ctx.Channel.TrySendMessageAsync(HubConstant.PayPalFriendAndFamilyImage);
        await ctx.Channel.ModifyAsync(a => a.Name = ctx.Interaction.GetTitleViaType(db.Type, Hub.Guild!.TryGetMember(db.UserId, out var mem) ? mem.Username : default));
        await ctx.Channel.TrySendMessageAsync(TicketHelper.GetPurchaseMessageFromType(db.Type, db));
        await ctx.TryEditResponseAsync($"{HubEmoji.MspHeart} Ticket type has been changed to **{type}**");
    }
}