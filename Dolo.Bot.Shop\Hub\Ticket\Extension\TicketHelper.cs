using Dolo.Bot.Shop.Hub.Ticket.Interaction;
using Dolo.Core.Discord;
using Dolo.Core.Extension;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Shop;
using DSharpPlus.EventArgs;
#pragma warning disable CS8604// Possible null reference argument.

namespace Dolo.Bot.Shop.Hub.Ticket.Extension;

/// <summary>
///     This class contains extension methods for handling tickets.
/// </summary>
public static class TicketHelper
{
    /// <summary>
    ///     The price for fame.
    /// </summary>
    private static int PriceFame
        => 35;

    /// <summary>
    ///     The price for autograph.
    /// </summary>
    private static int PriceAutograph
        => 20;

    /// <summary>
    ///     The price for PlutoPlus.
    /// </summary>
    private static int PricePlutoPlus
        => 10;

    /// <summary>
    ///     The price for Custom.
    /// </summary>
    private static int PriceOpChat
        => 25;


    /// <summary>
    ///     Gets the title based on the choice.
    /// </summary>
    /// <param name="choice">The choice.</param>
    /// <param name="e">The event arguments.</param>
    /// <returns>The title.</returns>
    public static string GetTitleViaChoice(this string choice, ComponentInteractionCreatedEventArgs e)
        => choice switch
        {
            ShopOption.Question => $"🤍⌯❓⌯{e.User.Username}",
            ShopOption.Autograph => $"🤍⌯📝⌯{e.User.Username}",
            ShopOption.PlutoPlus => $"🤍⌯🪐⌯{e.User.Username}",
            ShopOption.OpChat => $"🤍⌯🧼⌯{e.User.Username}",
            _ => $"🤍⌯{e.User.Username}"
        };

    /// <summary>
    ///     Gets the ticket type based on the choice.
    /// </summary>
    /// <param name="choice">The choice.</param>
    /// <returns>The ticket type.</returns>
    public static ShopTicketType GetTypeViaChoice(this string choice)
        => choice switch
        {
            ShopOption.Question => ShopTicketType.Question,
            ShopOption.Autograph => ShopTicketType.Autograph,
            ShopOption.PlutoPlus => ShopTicketType.PlutoPlus,
            ShopOption.OpChat => ShopTicketType.OpChat,
            _ => ShopTicketType.None
        };

    /// <summary>
    ///     Gets the title based on the ticket type.
    /// </summary>
    /// <param name="ctx">The interaction context.</param>
    /// <param name="type">The ticket type.</param>
    /// <param name="username">The username.</param>
    /// <returns>The title.</returns>
    public static string GetTitleViaType(this DiscordInteraction ctx, ShopTicketType type, string? username = default)
        => type switch
        {
            ShopTicketType.Question => $"🤍⌯❓⌯{username ?? ctx.User.Username}",
            ShopTicketType.Autograph => $"🤍⌯📝⌯{username ?? ctx.User.Username}",
            ShopTicketType.PlutoPlus => $"🤍⌯🪐⌯{username ?? ctx.User.Username}",
            ShopTicketType.OpChat => $"🤍⌯🧼⌯{username ?? ctx.User.Username}",
            _ => $"🤍⌯{username ?? ctx.User.Username}"
        };

    /// <summary>
    ///     Gets the payment message based on the choice.
    /// </summary>
    /// <param name="choice">The choice.</param>
    /// <param name="member">The ticket member.</param>
    /// <returns>The payment message.</returns>
    public static string GetPayMessageFromChoice(string choice, ShopTicketMember? member)
        => GetTypeViaChoice(choice) switch
        {
            ShopTicketType.Autograph => GetPurchaseMessage(member, PriceAutograph, "the autograph tool"),
            ShopTicketType.PlutoPlus => GetPurchaseMessage(member, PricePlutoPlus, "pluto+"),
            ShopTicketType.OpChat => GetPurchaseMessage(member, PriceOpChat, "the opchat tool"),
            _ => ""
        };

    /// <summary>
    ///     Gets the purchase message based on the ticket type.
    /// </summary>
    /// <param name="type">The ticket type.</param>
    /// <param name="member">The ticket member.</param>
    /// <returns>The purchase message.</returns>
    public static string GetPurchaseMessageFromType(ShopTicketType type, ShopTicketMember? member)
        => type switch
        {
            ShopTicketType.Autograph => GetPurchaseMessage(member, PriceAutograph, "the autograph tool"),
            ShopTicketType.PlutoPlus => GetPurchaseMessage(member, PricePlutoPlus, "pluto+"),
            ShopTicketType.OpChat => GetPurchaseMessage(member, PriceOpChat, "the opchat tool"),
            _ => ""
        };

    public static string GetPriceUrlFromType(ShopTicketType type)
        => type switch
        {
            ShopTicketType.Autograph => "https://cdn.discordapp.com/attachments/944722654907219988/1207789461128351754/20.png?ex=67288957&is=672737d7&hm=16b30302d9a44e4a457e17731433dd2e4100e57806d0edc5d1dc3836e52f356b&",
            ShopTicketType.PlutoPlus => "https://cdn.discordapp.com/attachments/944722654907219988/1120086990273118269/10.png?ex=65dba24c&is=65c92d4c&hm=0a4d73c42f855e22248a0ac54f21d61f8569426ca55017c2965a682930b10e34&",
            ShopTicketType.OpChat => "https://cdn.discordapp.com/attachments/944722654907219988/1097198330062307348/25.png?ex=67284ed2&is=6726fd52&hm=a8c00af9f0487ad8cf2193b2639cbbc95cda790059a0fa44dc93c58d6c62934f&",
            _
            => ""
        };

    /// <summary>
    ///     Gets the purchase message.
    /// </summary>
    /// <param name="member">The ticket member.</param>
    /// <param name="price">The price.</param>
    /// <param name="product">The product.</param>
    /// <returns>The purchase message.</returns>
    private static string GetPurchaseMessage(ShopTicketMember? member, int price, string product)
        => $"""
                ## hey there! {HubEmoji.MspHeart}

                - thank you for your interest in **{product}**
                - please send [**{price}€ paypal **](https://nothing.com) to the following email.

                `<EMAIL>`
                """;


    /// <summary>
    ///    The moderation components.
    /// </summary>
    public static DiscordComponent[] ModerationComponents = [
           new DiscordButtonComponent(DiscordButtonStyle.Primary, "pluto-shop-delete", "Delete", false, new DiscordComponentEmoji(HubEmoji.IceCube)),
           new DiscordButtonComponent(DiscordButtonStyle.Secondary, "pluto-shop-complete", "Archive", false, new DiscordComponentEmoji(HubEmoji.New))
           ];


    /// <summary>
    /// Checks if a user has an existing ticket and validates it.
    /// </summary>
    /// <param name="args">The interaction event arguments.</param>
    /// <param name="followupMessageId">The ID of the followup message to edit.</param>
    /// <returns>
    /// True if user has a valid ticket (and response was sent), 
    /// false if no valid ticket exists and new ticket flow should continue.
    /// </returns>
    public static async Task<bool> HasValidExistingTicketAsync(this ComponentInteractionCreatedEventArgs args, ulong followupMessageId)
    {
        // Check if the user already has an open ticket
        var ticket = await TicketDatabase.GetTicketAsync(args.User.Id);
        if (ticket is null)
        {
            // No existing ticket found
            return false;
        }

        // Check if the ticket's channel still exists
        var ticketChannel = args.Guild.TryGetChannel(ticket.ChannelId);
        if (ticketChannel is null)
        {
            // Channel was deleted, clean up the database
            await TicketDatabase.DeleteManyTicketAsync(ticket.UserId);
            return false;
        }

        // Ticket exists with valid channel, redirect user to their existing ticket
        await args.Interaction.TryEditFollowupMessageAsync(followupMessageId, $"Please see in {ticketChannel.Mention}");
        return true;
    }
}