using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Ticket.Interaction;

/// <summary>
/// Handles the interaction when a user clicks the buy button in the shop interface.
/// </summary>
public static class ShopBuyPressed
{
    // Stores the ID of the follow-up message to allow editing it later
    private static ulong _followupMessageId;

    /// <summary>
    /// Processes a buy button press interaction from a user.
    /// </summary>
    /// <param name="args">The interaction event arguments containing user and context information.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public static async Task InvokeBuyPressedAsync(this ComponentInteractionCreatedEventArgs args)
    {
        // Defer the response to give us time to process the request
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredChannelMessageWithSource, new DiscordInteractionResponseBuilder().AsEphemeral(true));

        // Send an initial "loading" message that we'll update later
        var followupMessage = await args.Interaction.TryCreateFollowupMessageAsync("Please wait...");
        _followupMessageId = followupMessage!.Id;

        // Check for existing ticket - if user has valid ticket, method handles the response
        if (await args.HasValidExistingTicketAsync(_followupMessageId))
            return;


        // No existing valid ticket found, show the product selection menu
        await args.CreateProductFollowupAsync();

    }

    /// <summary>
    /// Creates a product selection menu as a follow-up message.
    /// </summary>
    /// <param name="args">The interaction event arguments.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    private static async Task CreateProductFollowupAsync(this ComponentInteractionCreatedEventArgs args) => await args.Interaction.TryEditFollowupMessageAsync(_followupMessageId, new DiscordWebhookBuilder()
            .WithContent("Please select a product to purchase")
            .AddActionRowComponent(
                new DiscordSelectComponent("pluto-shop-option", "Product", [
                // Add Autograph Tool option with custom emoji
                new("Buy Autograph Tool", ShopOption.Autograph, "Purchase Autograph Tool", false, new DiscordComponentEmoji(HubEmoji.Autograph!)),
                // Add Pluto+ option with planet emoji
                new("Buy Pluto+", ShopOption.PlutoPlus, "Purchase Pluto+", false, new DiscordComponentEmoji("🪐")),
  ])), (error) => Console.WriteLine(error.Message));
}