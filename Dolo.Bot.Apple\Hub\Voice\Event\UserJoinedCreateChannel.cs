﻿using Dolo.Database;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Bot.Apple;

#pragma warning disable CS0618
namespace Dolo.Bot.Apple.Hub.Voice.Event;

public static class UserJoinedCreateChannel
{
    // will be triggered when the user joined the creation channel
    public static async Task InvokeAsync(VoiceArgs e)
    {
        if (e.Channel is null
            || e.Guild is null
            || e.User is null)
            return;


        // return if member not exist or voice channel is null
        if (HubChannel.Voice is null
            || Hub.Guild is null
            || !e.Guild.TryGetMember(e.User.Id, out var member))
            return;

        // try to get the database voice channel
        var users = await Mongo.Voice.GetOneAsync(a => a.Owner == e.User.Id);

        // if the channel exist than place the user in the channel
        if (users != null)
        {
            // get the channel from the guild
            var channel = e.Guild.TryGetChannel(users.Channel);

            // if null then kick the member
            if (channel is null)
            {
                await member.TryModifyAsync(a => a.VoiceChannel = null);
                await Mongo.Voice.DeleteAsync(a => a.Owner == e.User.Id);
                return;
            }

            // place member into channel again
            await channel.TryPlaceMemberAsync(member);
            return;
        }

        // create the voice channel
        var vc = await Hub.Guild.TryCreateVoiceChannelAsync($"🌸 » {e.User.Username}", HubChannel.VoiceTopic);

        // check if the channel is created if not kick the user
        if (vc is null)
        {
            await member.TryModifyAsync(a => a.VoiceChannel = null);
            return;
        }

        // add the user to the channel
        await vc.TryPlaceMemberAsync(member);

        // create a text channel with permissions
        var tc = await Hub.Guild.TryCreateTextChannelAsync("\ud83c\udf67\u232fchat", HubChannel.VoiceTopic, $"👑 **Channel-Owner »** <@{e.User.Id}>",
                 new List<DiscordOverwriteBuilder>
                 {
                     new DiscordOverwriteBuilder(member)
                         .Allow(DiscordPermission.ViewChannel)
                         .Allow(DiscordPermission.ReadMessageHistory),
                     new DiscordOverwriteBuilder(Hub.Guild.EveryoneRole)
                         .Deny(DiscordPermission.ViewChannel)
                         .Deny(DiscordPermission.ReadMessageHistory)
                 }, null, null, 0);

        if (tc != null)
            await tc.TrySendMessageAsync(HubEmbed.Voice(e.User));

        // add into database
        await Mongo.Voice.AddAsync(new VoiceUser
        {
            Owner = e.User.Id,
            Channel = vc.Id,
            TextChannel = tc?.Id ?? 0
        });
    }
}