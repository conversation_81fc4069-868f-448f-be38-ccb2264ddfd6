using Dolo.Bot.Shop.Hub.Ticket;
using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Pluto.Shard.Bot.Shop;
using System.Text;

namespace Dolo.Bot.Shop.Hub;

public static class HubEmbed
{
    public static DiscordMessageBuilder Buy
        => new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordButtonComponent(DiscordButtonStyle.Primary, "pluto-shop-buy", "Buy now", false, new(HubEmoji.WhiteHeart)),
                new DiscordButtonComponent(DiscordButtonStyle.Danger, "pluto-shop-question", "Question", false, new(HubEmoji.Boost))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                Color = new DiscordColor("#D6EDFB"),
                Description = new StringBuilder()
                    .AppendLine($"{HubEmoji.MspHeart} {HubEmoji.MspHeart} {HubEmoji.MspHeart}")
                    .AppendLine()
                    .AppendLine("🎗 **MovieStarPlanet Shop**")
                    .AppendLine("Our store offers you the possibility to use more features")
                    .AppendLine("that are helpful for you on moviestarplanet.")
                    .AppendLine()
                    .AppendLine("You can get access to many new features that")
                    .AppendLine("are not available on the free version.")
                    .AppendLine()
                    .AppendLine("💸 **Payments**")
                    .AppendLine("- [PayPal](https://msp.cbkdz.eu/)")
                    .AppendLine("- [Crypto](https://msp.cbkdz.eu/)")
                    .AppendLine()
                    .AppendLine("🎗 **Products**")
                    .AppendLine($"- {HubChannel.Autograph?.Mention}")
                    .AppendLine($"- {HubChannel.PlutoPlus?.Mention}")
                    .ToString(),
                ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281524008109604945/file-QBhelqzQy2mozH5Cb4pkhubx.png?ex=66dc07bd&is=66dab63d&hm=da64d3120e70f3f22990575dc4773b0c4650b7924c52309ec9b99fffe449a9bb&"
            });

    public static DiscordMessageBuilder FameTool
        => new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordLinkButtonComponent("https://msp.cbkdz.eu/fame", "Download", false, new(HubEmoji.Fame!))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                Color = new DiscordColor("#D6EDFB"),
                Title = $"{HubEmoji.Fame} {HubEmoji.Fame} {HubEmoji.Fame}",
                Description = new StringBuilder()
                    .AppendLine("‎")
                    .AppendLine($"{HubEmoji.Fame} **Fame**")
                    .AppendLine("A new software to earn unlimited fame.")
                    .AppendLine("Automated earning system without any bots!")
                    .AppendLine()
                    .AppendLine("🎗 **Simplification**")
                    .AppendLine("No bots and no bans with only one click.")
                    .AppendLine("you can earn unlimited fame.")
                    .AppendLine()
                    .AppendLine("🎗 **Piggy Bank**")
                    .AppendLine("You will not only get the fame on your account")
                    .AppendLine("but also in your piggy bank.")
                    .AppendLine()
                    .AppendLine("🎗 **X2 Boost**")
                    .AppendLine("You can also get a x2 boost whenever")
                    .AppendLine("you bought the fame boost")
                    .AppendLine()
                    .AppendLine($"{HubEmoji.Autograph} **Autograph Tool**")
                    .AppendLine("You will also get the autograph tool")
                    .AppendLine("for free with the fame tool.")
                    .AppendLine()
                    .AppendLine("[Buy now](https://ptb.discord.com/channels/1107409846183145475/1107591023758151710/1107953176604332074)")
                    .ToString(),
                ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1155171070521913394/Group_40.png"
            });

    public static DiscordMessageBuilder AutographTool
        => new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordLinkButtonComponent("https://msp.cbkdz.eu/", "Download", false, new(HubEmoji.Autograph!)),
                new DiscordButtonComponent(DiscordButtonStyle.Primary, "pluto-autograph-preview", "Preview", false, new(HubEmoji.Autograph!))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                Thumbnail = new()
                {
                    Url = TicketHelper.GetPriceUrlFromType(ShopTicketType.Autograph)
                },
                Color = new DiscordColor("#D6EDFB"),
                Title = $"{HubEmoji.Autograph} {HubEmoji.Autograph} {HubEmoji.Autograph}",
                Description = new StringBuilder()
                    .AppendLine("‎")
                    .AppendLine($"{HubEmoji.Autograph} **Autograph**")
                    .AppendLine("A new software to get autographs.")
                    .AppendLine("Automated autograph system without any bots!")
                    .AppendLine()
                    .AppendLine("🎗 **Simplification**")
                    .AppendLine("Easily switch between your accounts.")
                    .AppendLine("You can get autographs on all your accounts.")
                    .AppendLine()
                    .AppendLine("🎗 **No Limit**")
                    .AppendLine("No matter how many accounts you have.")
                    .AppendLine("Add as many accounts as you like..")
                    .AppendLine()
                    .AppendLine("[Buy now](https://ptb.discord.com/channels/1107409846183145475/1107591023758151710/1107953176604332074)")
                    .ToString(),
                ImageUrl = "https://media.discordapp.net/attachments/944722654907219988/1399779720924626985/Untitled2.jpg?ex=688a3df4&is=6888ec74&hm=45e94dff19231062e1e15ba18346972a765d8dfd5a55c9ee5e2b295745a0fd20&=&format=webp"
            });

    public static DiscordMessageBuilder PlutoPlus
        => new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordLinkButtonComponent("https://msp.cbkdz.eu/", "Download", false, new(HubEmoji.Pluto!))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                Thumbnail = new()
                {
                    Url = TicketHelper.GetPriceUrlFromType(ShopTicketType.PlutoPlus)
                },
                Color = new DiscordColor("#D6EDFB"),
                Title = $"{HubEmoji.Diamonds} {HubEmoji.Diamonds} {HubEmoji.Diamonds}",
                Description = new StringBuilder()
                    .AppendLine("‎")
                    .AppendLine($"{HubEmoji.Pluto} **Pluto+**")
                    .AppendLine("Supercharge your experience on pluto.")
                    .AppendLine("Get access to secret features and more.")
                    .AppendLine()
                    .AppendLine("🎗 **About**")
                    .AppendLine("Pluto+ is a paid version of pluto.")
                    .AppendLine("It offers you more features and more capabilities.")
                    .AppendLine()
                    .AppendLine("💸 **Purchase**")
                    .AppendLine("You can buy pluto+ for a one time payment.")
                    .AppendLine("We do not offer any subscriptions.")
                    .AppendLine()
                    .AppendLine("🎗 **Features**")
                    .AppendLine($"{HubEmoji.MspLove} ⋅ Access to farming system")
                    .AppendLine($"{HubEmoji.MspLove} ⋅ Access to autograph feature in pluto")
                    .AppendLine($"{HubEmoji.MspLove} ⋅ Access to plus only tool features")
                    .AppendLine($"{HubEmoji.MspLove} ⋅ Access to any new upcoming features")
                    .AppendLine()
                    .AppendLine("[Buy now](https://ptb.discord.com/channels/1107409846183145475/1107591023758151710/1107953176604332074)")
                    .ToString(),
                ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281524232257540158/file-rHKRPKQQNWeC6knTgUoZr34d.png?ex=66dc07f2&is=66dab672&hm=0ee218477aae1820acf5fcc6ddd497f11ef2c52c1b41d2f9598eb2652a0bce25&"
            });

    public static DiscordMessageBuilder Custom
        => new DiscordMessageBuilder().AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor("#D6EDFB"),
            Title = $"{HubEmoji.MspLove} {HubEmoji.MspLove} {HubEmoji.MspLove}",
            Description = new StringBuilder()
                .AppendLine("‎")
                .AppendLine("💊 **Custom Tool**")
                .AppendLine("You want a custom tool for msp?")
                .AppendLine("We can develop it for you.")
                .AppendLine()
                .AppendLine("🎗 **MSP 1/2**")
                .AppendLine("No matter what you want to have.")
                .AppendLine("We can develop it for you on msp 1 or 2")
                .AppendLine()
                .AppendLine("💸 **Pricing**")
                .AppendLine("The price depends on the complexity of the tool.")
                .AppendLine("We will discuss the price with you.")
                .AppendLine()
                .AppendLine("[Buy now](https://ptb.discord.com/channels/1107409846183145475/1107591023758151710/1107953176604332074)")
                .ToString(),
            ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281524325727604736/file-iDxE7TW9heVBEmHJjHYuicas.png?ex=66dc0808&is=66dab688&hm=11ea23c2de5f8298936ddd4611919195a3c7f76f28aa3c5987e5f33c896a9988&"
        });

    public static DiscordMessageBuilder OpChat
        => new DiscordMessageBuilder().AddEmbed(new DiscordEmbedBuilder
        {
            Thumbnail = new()
            {
                Url = TicketHelper.GetPriceUrlFromType(ShopTicketType.OpChat)
            },
            Color = new DiscordColor("#D6EDFB"),
            Title = $"{HubEmoji.MspLove} {HubEmoji.MspLove} {HubEmoji.MspLove}",
            Description = new StringBuilder()
                .AppendLine("‎")
                .AppendLine("🧼 **Op Chat**")
                .AppendLine("A new advanced chatroom fun tool.")
                .AppendLine("Make other player jealous and cry!")
                .AppendLine()
                .AppendLine("🎗 **Movement**")
                .AppendLine("Walk around the entire chatroom.")
                .AppendLine("You can also walk out of the chatroom.")
                .AppendLine()
                .AppendLine("🎗 **No Vip**")
                .AppendLine("You can use any color and emotes")
                .AppendLine("without having vip.")
                .AppendLine()
                .AppendLine("[Buy now](https://ptb.discord.com/channels/1107409846183145475/1107591023758151710/1107953176604332074)")
                .ToString(),
            ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281524435211522110/file-XmPZiqHo0xugKkgKcH4WmJIZ.png?ex=66dc0822&is=66dab6a2&hm=9ccad9f5aaaa788263f33240208f9296e7fa9acbc4fc1c61b00f837cdaa3dd55&"
        });

    public static DiscordMessageBuilder IdeaInfo
        => new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordButtonComponent(DiscordButtonStyle.Primary, "suggest-idea", "Suggest Idea", false, new(HubEmoji.MspHeart!))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                Color = new DiscordColor("#2B2D31"),
                ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281524557714428067/file-MDxGWfylQ3oyashEGOpDGVBk.png?ex=66dc0840&is=66dab6c0&hm=ef225d101af302bc88d340a5a8ee1d89010dc7b96d8ed1df4f1d6c2366315c4f&"
            });

    public static async Task<DiscordMessageBuilder> RewardsAsync()
    {
        return new DiscordMessageBuilder()
            .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
            {
                new DiscordButtonComponent(DiscordButtonStyle.Primary, "reward-my-invites", "My Invites", false, new(HubEmoji.MspHeart!)),
                new DiscordButtonComponent(DiscordButtonStyle.Success, "reward-create-invite", "Create Invite", false, new(HubEmoji.MspHeart!))
            }))
            .AddEmbed(new DiscordEmbedBuilder
            {
                ImageUrl = "https://media.discordapp.net/attachments/944722654907219988/1281523731654512641/file-brdGCWxGHbzQP2HQFseA4SSb.png?ex=66dc077b&is=66dab5fb&hm=d1663f1bb366c4957131ff985a029be47c60352a4cd11cbed24a77dda635c941&=&format=webp&quality=lossless&width=550&height=314",
                Color = new DiscordColor("#2B2D31"),
                Footer = new()
                {
                    Text = "dont forget to invite your friends!",
                    IconUrl = "https://cdn.discordapp.com/emojis/1205846416677929060.gif?size=44&quality=lossless"
                },
                Description = $"""
                     ## Rewards {HubEmoji.New}
                    - *invite people to get access to our rewards.*
                    - *you can only get the rewards if you have invites*
                    ### Rewards
                    - 🎁 • Amazon Gift Card
                    - 🎁 • Netflix Gift Card
                    - 🎮 • Steam Gift Card
                    - 🎟 • Discord Nitro
                    - 💳 • Paysafecard
                    - 🌟 • MSP StarVIP
                    - 🔝 • MSP TopUp
                    """
            });
    }

    public static DiscordMessageBuilder NewIdea(this DiscordInteraction interaction, string idea)
        => new DiscordMessageBuilder()
            .AddEmbed(new DiscordEmbedBuilder
            {
                Color = new DiscordColor("#2B2D31"),
                Description = $"## `\ud83e\udde9` NEW Idea! \n{interaction.User.Mention}\n```{idea}```",
                Footer = new()
                {
                    Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss")
                },
                Thumbnail = new()
                {
                    Url = interaction.User.AvatarUrl
                }
            });

    public static DiscordMessageBuilder NewUser(DiscordMember member, string? msg)
        => new DiscordMessageBuilder()
            .WithContent(member.Mention)
            .AddEmbed(new DiscordEmbedBuilder
            {
                Color = new DiscordColor("#2B2D31"),
                Title = $"Welcome! {member.Username} {HubEmoji.MspLove}",
                Description = new StringBuilder()
                    .AppendLine()
                    .AppendLine($"`{Hub.Guild?.MemberCount:N0} Member`")
                    .AppendLine($"**»** <#{HubChannel.Rewards?.Id}>")
                    .AppendLine($"**»** <#{HubChannel.Idea?.Id}>")
                    .AppendLine($"**»** <#{HubChannel.Buy?.Id}>")
                    .ToString(),
                Thumbnail = new()
                {
                    Url = member.AvatarUrl
                },
                Footer = new()
                {
                    Text = msg,
                    IconUrl = Hub.Guild?.IconUrl
                }
            });


}
